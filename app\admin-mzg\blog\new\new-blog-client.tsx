"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ArrowLeft, Save } from "lucide-react"
import Link from "next/link"
import { saveBlogPost, getAllCategories } from "@/app/actions/blog-actions-db-final"
import type { BlogCategory, ContentImage } from "@/app/actions/blog-actions-db-final"
import ImageUploadZone from "@/components/admin/image-upload-zone"
import FeaturedImageUpload from "@/components/admin/featured-image-upload"

export default function NewBlogPostPageClient() {
  const router = useRouter()
  const [categories, setCategories] = useState<BlogCategory[]>([])
  const [loading, setLoading] = useState(false)
  const [contentImages, setContentImages] = useState<ContentImage[]>([])
  const [formData, setFormData] = useState({
    title: "",
    slug: "",
    excerpt: "",
    content: "",
    categoryId: "",
    author: "",
    imageUrl: "",
    featuredImageAlt: "",
    published: false,
    metaTitle: "",
    metaDescription: "",
    tags: "",
  })

  useEffect(() => {
    loadCategories()
  }, [])

  const loadCategories = async () => {
    try {
      const data = await getAllCategories()
      setCategories(data)
    } catch (error) {
      console.error("Failed to load categories:", error)
    }
  }

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, "")
      .replace(/\s+/g, "-")
      .replace(/-+/g, "-")
      .trim()
  }

  const handleTitleChange = (value: string) => {
    setFormData(prev => ({
      ...prev,
      title: value,
      slug: generateSlug(value),
      metaTitle: value.length <= 60 ? value : prev.metaTitle
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Client-side validation
    if (!formData.title.trim()) {
      alert("Title is required")
      return
    }
    if (!formData.slug.trim()) {
      alert("Slug is required")
      return
    }
    if (!formData.excerpt.trim()) {
      alert("Excerpt is required")
      return
    }
    if (!formData.content.trim()) {
      alert("Content is required")
      return
    }
    if (!formData.author.trim()) {
      alert("Author is required")
      return
    }
    if (!formData.categoryId) {
      alert("Please select a category")
      return
    }

    setLoading(true)

    try {
      const submitData = new FormData()
      Object.entries(formData).forEach(([key, value]) => {
        if (key === "published") {
          submitData.append(key, value.toString())
        } else if (key === "categoryId") {
          submitData.append(key, value.toString())
        } else if (key === "tags") {
          // Ensure tags is always a string, even if empty
          submitData.append(key, (value as string) || "")
        } else {
          // Only append non-empty values for optional fields
          if (value && typeof value === "string" && value.trim()) {
            submitData.append(key, value)
          } else if (["title", "slug", "excerpt", "content", "author"].includes(key)) {
            // These are required fields, append even if empty (server will validate)
            submitData.append(key, value as string)
          }
        }
      })

      // Add content images
      if (contentImages.length > 0) {
        submitData.append('contentImages', JSON.stringify(contentImages))
      }

      const result = await saveBlogPost(submitData)

      if (result.success) {
        router.push("/admin-mzg/blog")
      } else {
        alert(result.message)
      }
    } catch (error) {
      console.error("Failed to save post:", error)
      alert("Failed to save post. Please check all required fields.")
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto py-8 max-w-7xl px-4">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Link href="/admin-mzg/blog">
          <Button variant="outline" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Posts
          </Button>
        </Link>
        <h1 className="text-3xl font-bold">Create New Blog Post</h1>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Main Content */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Post Content</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="title">Title *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => handleTitleChange(e.target.value)}
                    placeholder="Enter post title"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="slug">Slug *</Label>
                  <Input
                    id="slug"
                    value={formData.slug}
                    onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                    placeholder="post-url-slug"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="excerpt">Excerpt *</Label>
                  <Textarea
                    id="excerpt"
                    value={formData.excerpt}
                    onChange={(e) => setFormData(prev => ({ ...prev, excerpt: e.target.value }))}
                    placeholder="Brief description of the post"
                    rows={3}
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="content">Content * (Markdown Supported)</Label>
                  <div className="mb-2 text-sm text-gray-600">
                    <details className="cursor-pointer">
                      <summary className="font-medium">Markdown Syntax Guide</summary>
                      <div className="mt-2 p-3 bg-gray-50 rounded text-xs space-y-1">
                        <div><strong>Headers:</strong> # H1, ## H2, ### H3</div>
                        <div><strong>Bold:</strong> **bold text**</div>
                        <div><strong>Italic:</strong> *italic text*</div>
                        <div><strong>Lists:</strong> - item or 1. item</div>
                        <div><strong>Links:</strong> [text](url)</div>
                        <div><strong>Images:</strong> ![alt](url) or ![alt](url){"{align:left}"}</div>
                        <div><strong>Code:</strong> `inline code` or ```code block```</div>
                        <div><strong>Tables:</strong> | Header | Header | (use | to separate columns)</div>
                        <div><strong>Quotes:</strong> &gt; quoted text</div>
                      </div>
                    </details>
                  </div>
                  <Textarea
                    id="content"
                    value={formData.content}
                    onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                    placeholder="Write your post content here using Markdown syntax...

Example:
# Main Title
This is a paragraph with **bold** and *italic* text.

## Subheading
- List item 1
- List item 2

> This is a quote

[Link text](https://example.com)

![Image description](image-url){align:center}"
                    rows={15}
                    required
                  />
                </div>
              </CardContent>
            </Card>

            {/* Content Images */}
            <Card>
              <CardHeader>
                <CardTitle>Content Images</CardTitle>
                <p className="text-sm text-gray-600">
                  Upload images that will be automatically inserted into your article content.
                  Images will be intelligently positioned throughout the text for better readability.
                </p>
              </CardHeader>
              <CardContent>
                <ImageUploadZone
                  images={contentImages}
                  onImagesChange={setContentImages}
                  maxImages={8}
                />
              </CardContent>
            </Card>

            {/* SEO Settings */}
            <Card>
              <CardHeader>
                <CardTitle>SEO Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="metaTitle">Meta Title</Label>
                  <Input
                    id="metaTitle"
                    value={formData.metaTitle}
                    onChange={(e) => setFormData(prev => ({ ...prev, metaTitle: e.target.value }))}
                    placeholder="SEO title (max 60 characters)"
                    maxLength={60}
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    {formData.metaTitle.length}/60 characters
                  </p>
                </div>

                <div>
                  <Label htmlFor="metaDescription">Meta Description</Label>
                  <Textarea
                    id="metaDescription"
                    value={formData.metaDescription}
                    onChange={(e) => setFormData(prev => ({ ...prev, metaDescription: e.target.value }))}
                    placeholder="SEO description (max 160 characters)"
                    rows={3}
                    maxLength={160}
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    {formData.metaDescription.length}/160 characters
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Publish Settings */}
            <Card>
              <CardHeader>
                <CardTitle>Publish Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="published">Published</Label>
                  <Switch
                    id="published"
                    checked={formData.published}
                    onCheckedChange={(checked) =>
                      setFormData(prev => ({ ...prev, published: checked }))
                    }
                  />
                </div>

                <div>
                  <Label htmlFor="author">Author *</Label>
                  <Input
                    id="author"
                    value={formData.author}
                    onChange={(e) => setFormData(prev => ({ ...prev, author: e.target.value }))}
                    placeholder="Author name"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="categoryId">Category *</Label>
                  <Select
                    value={formData.categoryId}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, categoryId: value }))}
                    required
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.id.toString()}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="tags">Tags</Label>
                  <Input
                    id="tags"
                    value={formData.tags}
                    onChange={(e) => setFormData(prev => ({ ...prev, tags: e.target.value }))}
                    placeholder="tag1, tag2, tag3"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    Separate tags with commas
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Featured Image */}
            <Card>
              <CardHeader>
                <CardTitle>Featured Image</CardTitle>
                <p className="text-sm text-gray-600">
                  Upload a featured image that will be displayed in the blog list and at the top of your post.
                </p>
              </CardHeader>
              <CardContent>
                <FeaturedImageUpload
                  imageUrl={formData.imageUrl}
                  altText={formData.featuredImageAlt}
                  onImageChange={(url) => setFormData(prev => ({ ...prev, imageUrl: url }))}
                  onAltTextChange={(alt) => setFormData(prev => ({ ...prev, featuredImageAlt: alt }))}
                  onImageDelete={() => setFormData(prev => ({ ...prev, imageUrl: "", featuredImageAlt: "" }))}
                />
              </CardContent>
            </Card>

            {/* Actions */}
            <div className="space-y-2">
              <Button type="submit" className="w-full" disabled={loading}>
                <Save className="h-4 w-4 mr-2" />
                {loading ? "Saving..." : "Save Post"}
              </Button>
              <Link href="/admin-mzg/blog" className="block">
                <Button type="button" variant="outline" className="w-full">
                  Cancel
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </form>
    </div>
  )
}

/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  experimental: {
    optimizePackageImports: ['@radix-ui/react-icons', 'lucide-react'],
    // 启用更激进的优化
    optimizeServerReact: true,
  },
  // 优化 serverless function 大小
  serverExternalPackages: ['better-sqlite3', 'sqlite3'],
  // 输出配置
  output: 'standalone',
  images: {
    unoptimized: false,
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'hebbkx1anhila5yf.public.blob.vercel-storage.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: '*.public.blob.vercel-storage.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'via.placeholder.com',
        port: '',
        pathname: '/**',
      }
    ],
  },
  ...(process.env.NODE_ENV === 'development' && {
    onDemandEntries: {
      maxInactiveAge: 25 * 1000,
      pagesBufferLength: 2,
    },
  }),
  webpack: (config, { dev, isServer }) => {
    if (!dev) {
      config.resolve.alias = {
        ...config.resolve.alias,
        '@stagewise/toolbar-next': false,
      }
    }

    // 优化服务器端打包
    if (isServer) {
      config.externals = config.externals || []
      config.externals.push({
        'better-sqlite3': 'commonjs better-sqlite3',
        'sqlite3': 'commonjs sqlite3',
        'bcrypt': 'commonjs bcrypt',
        'fs': 'commonjs fs',
        'path': 'commonjs path',
        'crypto': 'commonjs crypto',
        'os': 'commonjs os',
      })

      // 减少 bundle 大小
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          chunks: 'all',
          cacheGroups: {
            default: false,
            vendors: false,
            // 将大型库分离
            vendor: {
              name: 'vendor',
              chunks: 'all',
              test: /node_modules/,
              priority: 20
            },
            // 将 Radix UI 组件分离
            radix: {
              name: 'radix',
              chunks: 'all',
              test: /node_modules\/@radix-ui/,
              priority: 30
            }
          }
        }
      }
    }

    return config
  },
}

export default nextConfig
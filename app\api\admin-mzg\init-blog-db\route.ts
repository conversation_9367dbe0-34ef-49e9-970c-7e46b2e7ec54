import { NextRequest, NextResponse } from "next/server"
import { sql } from "@/lib/db"

// 强制动态渲染
export const dynamic = 'force-dynamic'
import fs from "fs"
import path from "path"

export async function POST(request: NextRequest) {
  try {
    // 读取迁移脚本
    const migrationPath = path.join(process.cwd(), "scripts", "migrate-blog-to-db.sql")
    const migrationSQL = fs.readFileSync(migrationPath, "utf8")

    // 执行迁移脚本
    // 分割SQL语句并逐个执行
    const statements = migrationSQL
      .split(";")
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0)

    for (const statement of statements) {
      if (statement.trim()) {
        await sql.unsafe(statement)
      }
    }

    return NextResponse.json({
      success: true,
      message: "Blog database initialized successfully",
    })
  } catch (error) {
    console.error("Error initializing blog database:", error)
    return NextResponse.json(
      {
        success: false,
        message: "Failed to initialize blog database",
        error: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  try {
    // 检查数据库表是否存在
    const tables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('blog_posts', 'blog_categories', 'blog_tags', 'blog_post_tags')
      ORDER BY table_name
    `

    const existingTables = tables.map(t => t.table_name)
    const requiredTables = ['blog_categories', 'blog_post_tags', 'blog_posts', 'blog_tags']
    const missingTables = requiredTables.filter(table => !existingTables.includes(table))

    // 获取每个表的记录数
    const stats: Record<string, number> = {}
    for (const table of existingTables) {
      try {
        const countResult = await sql`SELECT COUNT(*) as count FROM ${sql.unsafe(table)}`
        stats[table] = parseInt(countResult[0].count as string)
      } catch (error) {
        stats[table] = 0
      }
    }

    return NextResponse.json({
      success: true,
      tables: {
        existing: existingTables,
        missing: missingTables,
        required: requiredTables,
      },
      stats,
      isInitialized: missingTables.length === 0,
    })
  } catch (error) {
    console.error("Error checking blog database status:", error)
    return NextResponse.json(
      {
        success: false,
        message: "Failed to check blog database status",
        error: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    )
  }
}
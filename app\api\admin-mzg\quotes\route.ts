import { NextRequest, NextResponse } from 'next/server'
import { sql } from '@/lib/database'

// 强制动态渲染
export const dynamic = 'force-dynamic'

// 获取所有咨询记录
export async function GET(request: NextRequest) {
  try {
    // 查询所有咨询记录
    const consultations = await sql`
      SELECT * FROM consultation 
      ORDER BY created_at DESC
    `

    return NextResponse.json({
      success: true,
      consultations
    })
  } catch (error) {
    console.error('获取咨询记录失败:', error)
    return NextResponse.json(
      { success: false, message: '获取数据失败' },
      { status: 500 }
    )
  }
}

// 创建新的咨询记录（手动添加）
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      name,
      email,
      company,
      phone,
      partDetails,
      source = 'manual',
      productPagePath,
      productName,
      status = 'new',
      priority = 'normal',
      assignedTo,
      notes,
      followUpDate
    } = body

    // 验证必填字段
    if (!name || !email || !company || !phone || !partDetails) {
      return NextResponse.json(
        { success: false, message: '请填写所有必填字段' },
        { status: 400 }
      )
    }

    const result = await sql`
      INSERT INTO consultation (
        name, email, company, phone, part_details,
        attachment_urls, attachment_metadata, source,
        product_page_path, product_name, status, priority,
        assigned_to, notes, follow_up_date
      ) VALUES (
        ${name},
        ${email},
        ${company},
        ${phone},
        ${partDetails},
        ${'[]'}, 
        ${'[]'},
        ${source},
        ${productPagePath || ''},
        ${productName || ''},
        ${status},
        ${priority},
        ${assignedTo || null},
        ${notes || null},
        ${followUpDate || null}
      )
      RETURNING id
    `

    return NextResponse.json({
      success: true,
      message: '咨询记录创建成功',
      data: { id: result[0].id }
    })

  } catch (error) {
    console.error('创建咨询记录失败:', error)
    return NextResponse.json(
      { success: false, message: '创建失败' },
      { status: 500 }
    )
  }
} 
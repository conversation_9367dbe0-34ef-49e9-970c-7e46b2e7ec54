import { NextRequest, NextResponse } from 'next/server'
import { sql } from '@/lib/database'

// 强制动态渲染
export const dynamic = 'force-dynamic'

// 获取单个咨询记录
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    if (isNaN(id)) {
      return NextResponse.json(
        { success: false, message: '无效的ID' },
        { status: 400 }
      )
    }

    const consultation = await sql`
      SELECT * FROM consultation WHERE id = ${id}
    `

    if (consultation.length === 0) {
      return NextResponse.json(
        { success: false, message: '咨询记录不存在' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      consultation: consultation[0]
    })
  } catch (error) {
    console.error('获取咨询记录失败:', error)
    return NextResponse.json(
      { success: false, message: '获取数据失败' },
      { status: 500 }
    )
  }
}

// 更新咨询记录
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    if (isNaN(id)) {
      return NextResponse.json(
        { success: false, message: '无效的ID' },
        { status: 400 }
      )
    }

    const body = await request.json()
    const {
      name,
      email,
      company,
      phone,
      part_details,
      status,
      priority,
      assigned_to,
      notes,
      follow_up_date
    } = body

    // 构建更新字段和值
    const updateData: any = {}
    
    if (name !== undefined) updateData.name = name
    if (email !== undefined) updateData.email = email
    if (company !== undefined) updateData.company = company
    if (phone !== undefined) updateData.phone = phone
    if (part_details !== undefined) updateData.part_details = part_details
    if (status !== undefined) updateData.status = status
    if (priority !== undefined) updateData.priority = priority
    if (assigned_to !== undefined) updateData.assigned_to = assigned_to
    if (notes !== undefined) updateData.notes = notes
    if (follow_up_date !== undefined) updateData.follow_up_date = follow_up_date
    
    if (Object.keys(updateData).length === 0) {
      return NextResponse.json(
        { success: false, message: '没有提供要更新的字段' },
        { status: 400 }
      )
    }

    // 使用模板字符串更新
    let result
    
    if (name !== undefined) {
      result = await sql`UPDATE consultation SET name = ${name}, updated_at = CURRENT_TIMESTAMP WHERE id = ${id} RETURNING *`
    } else if (email !== undefined) {
      result = await sql`UPDATE consultation SET email = ${email}, updated_at = CURRENT_TIMESTAMP WHERE id = ${id} RETURNING *`
    } else if (company !== undefined) {
      result = await sql`UPDATE consultation SET company = ${company}, updated_at = CURRENT_TIMESTAMP WHERE id = ${id} RETURNING *`
    } else if (phone !== undefined) {
      result = await sql`UPDATE consultation SET phone = ${phone}, updated_at = CURRENT_TIMESTAMP WHERE id = ${id} RETURNING *`
    } else if (part_details !== undefined) {
      result = await sql`UPDATE consultation SET part_details = ${part_details}, updated_at = CURRENT_TIMESTAMP WHERE id = ${id} RETURNING *`
    } else if (status !== undefined) {
      result = await sql`UPDATE consultation SET status = ${status}, updated_at = CURRENT_TIMESTAMP WHERE id = ${id} RETURNING *`
    } else if (priority !== undefined) {
      result = await sql`UPDATE consultation SET priority = ${priority}, updated_at = CURRENT_TIMESTAMP WHERE id = ${id} RETURNING *`
    } else if (assigned_to !== undefined) {
      result = await sql`UPDATE consultation SET assigned_to = ${assigned_to}, updated_at = CURRENT_TIMESTAMP WHERE id = ${id} RETURNING *`
    } else if (notes !== undefined) {
      result = await sql`UPDATE consultation SET notes = ${notes}, updated_at = CURRENT_TIMESTAMP WHERE id = ${id} RETURNING *`
    } else if (follow_up_date !== undefined) {
      result = await sql`UPDATE consultation SET follow_up_date = ${follow_up_date}, updated_at = CURRENT_TIMESTAMP WHERE id = ${id} RETURNING *`
    }
    
    if (!result || result.length === 0) {
      return NextResponse.json(
        { success: false, message: '咨询记录不存在' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: '更新成功',
      consultation: result[0]
    })

  } catch (error) {
    console.error('更新咨询记录失败:', error)
    return NextResponse.json(
      { success: false, message: '更新失败' },
      { status: 500 }
    )
  }
}

// 删除咨询记录
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    if (isNaN(id)) {
      return NextResponse.json(
        { success: false, message: '无效的ID' },
        { status: 400 }
      )
    }

    // 先获取记录信息（用于清理附件文件）
    const consultationData = await sql`
      SELECT attachment_metadata FROM consultation WHERE id = ${id}
    `
    
    // 删除记录
    const result = await sql`
      DELETE FROM consultation WHERE id = ${id}
    `
    
    // PostgreSQL的DELETE不返回changes，我们检查consultationData是否存在
    if (consultationData.length === 0) {
      return NextResponse.json(
        { success: false, message: '咨询记录不存在' },
        { status: 404 }
      )
    }

    // TODO: 可以在这里添加清理附件文件的逻辑
    // if (consultation?.attachment_metadata) {
    //   const attachments = JSON.parse(consultation.attachment_metadata)
    //   // 删除物理文件
    // }

    return NextResponse.json({
      success: true,
      message: '删除成功'
    })

  } catch (error) {
    console.error('删除咨询记录失败:', error)
    return NextResponse.json(
      { success: false, message: '删除失败' },
      { status: 500 }
    )
  }
} 
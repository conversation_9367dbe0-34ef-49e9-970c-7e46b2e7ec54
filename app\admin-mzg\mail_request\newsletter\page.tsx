"use client"

// 强制动态渲染
export const dynamic = 'force-dynamic'

import { useEffect, useState } from "react"
import Link from "next/link"
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Trash2, Search, Mail, Calendar, MapPin, RefreshCw, ArrowLeft, Home, Download } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface NewsletterSubscription {
  id: number
  subscribe_mail: string
  ip_address: string
  created_at: string
  is_active: boolean
}

export default function NewsletterManagement() {
  const [subscriptions, setSubscriptions] = useState<NewsletterSubscription[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [message, setMessage] = useState('')
  const [messageType, setMessageType] = useState<'success' | 'error' | ''>('')

  // 获取订阅列表
  const fetchSubscriptions = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/newsletter')
      const data = await response.json()
      
      if (data.success) {
        setSubscriptions(data.data)
      } else {
        setMessage('获取订阅列表失败')
        setMessageType('error')
      }
    } catch (error) {
      setMessage('网络错误，无法获取订阅列表')
      setMessageType('error')
    } finally {
      setLoading(false)
    }
  }

  // 删除订阅
  const handleDelete = async (id: number, email: string) => {
    if (!confirm(`确定要删除订阅邮箱 "${email}" 吗？`)) {
      return
    }

    try {
      const response = await fetch(`/api/newsletter/${id}`, {
        method: 'DELETE',
      })
      
      const data = await response.json()
      
      if (data.success) {
        setMessage('删除成功')
        setMessageType('success')
        fetchSubscriptions() // 重新加载列表
      } else {
        setMessage(data.error || '删除失败')
        setMessageType('error')
      }
    } catch (error) {
      setMessage('删除失败，请稍后重试')
      setMessageType('error')
    }

    // 3秒后清除消息
    setTimeout(() => {
      setMessage('')
      setMessageType('')
    }, 3000)
  }

  // 初始化数据表
  const initializeTables = async () => {
    try {
      const response = await fetch('/api/admin-mzg/init-newsletter', {
        method: 'POST',
      })
      
      const data = await response.json()
      
      if (data.success) {
        setMessage('数据表初始化成功')
        setMessageType('success')
        fetchSubscriptions()
      } else {
        setMessage(data.error || '初始化失败')
        setMessageType('error')
      }
    } catch (error) {
      setMessage('初始化失败，请稍后重试')
      setMessageType('error')
    }

    setTimeout(() => {
      setMessage('')
      setMessageType('')
    }, 3000)
  }

  // 导出数据
  const handleExportData = async () => {
    try {
      setMessage('正在导出数据...')
      setMessageType('success')

      const response = await fetch('/api/newsletter/export', {
        method: 'GET',
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `newsletter-subscriptions-${new Date().toISOString().split('T')[0]}.csv`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        window.URL.revokeObjectURL(url)

        setMessage('数据导出成功 (CSV格式，可用Excel打开)')
        setMessageType('success')
      } else {
        const data = await response.json()
        setMessage(data.error || '导出失败')
        setMessageType('error')
      }
    } catch (error) {
      setMessage('导出失败，请稍后重试')
      setMessageType('error')
    }

    setTimeout(() => {
      setMessage('')
      setMessageType('')
    }, 3000)
  }

  useEffect(() => {
    fetchSubscriptions()
  }, [])

  // 过滤订阅列表
  const filteredSubscriptions = subscriptions.filter(sub =>
    sub.subscribe_mail.toLowerCase().includes(searchTerm.toLowerCase()) ||
    sub.ip_address.includes(searchTerm)
  )

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN')
  }

  return (
    <div className="w-full px-6 py-6">
      {/* 面包屑导航 */}
      <div className="flex items-center gap-2 mb-6 text-sm text-gray-600">
        <Link href="/admin-mzg" className="flex items-center hover:text-gray-900">
          <Home className="h-4 w-4 mr-1" />
          管理首页
        </Link>
        <span>→</span>
        <Link href="/admin-mzg/mail_request" className="hover:text-gray-900">
          邮件询价
        </Link>
        <span>→</span>
        <span className="text-gray-900 font-medium">Newsletter管理</span>
      </div>

      {/* 返回按钮和标题 */}
      <div className="flex items-center gap-4 mb-6">
        <Link href="/admin-mzg/mail_request">
          <Button variant="outline" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回邮件询价
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold">Newsletter 管理</h1>
          <p className="text-gray-600 mt-1">管理用户Newsletter订阅信息</p>
        </div>
      </div>

      {/* 操作按钮和搜索 */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="flex gap-2">
          <Button onClick={initializeTables} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            初始化数据表
          </Button>
          <Button onClick={fetchSubscriptions} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新列表
          </Button>
          <Button onClick={handleExportData} variant="outline" className="bg-green-50 hover:bg-green-100 border-green-200">
            <Download className="h-4 w-4 mr-2" />
            导出数据
          </Button>
        </div>
        <div className="flex-1 max-w-md">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="搜索邮箱或IP地址..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
      </div>

      {/* 消息提示 */}
      {message && (
        <Alert className={`mb-6 ${messageType === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
          <AlertDescription className={messageType === 'success' ? 'text-green-700' : 'text-red-700'}>
            {message}
          </AlertDescription>
        </Alert>
      )}

      {/* 统计信息 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <Mail className="h-8 w-8 text-blue-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">总订阅数</p>
                <p className="text-2xl font-bold">{subscriptions.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <Calendar className="h-8 w-8 text-green-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">今日新增</p>
                <p className="text-2xl font-bold">
                  {subscriptions.filter(sub => {
                    const today = new Date().toDateString()
                    const subDate = new Date(sub.created_at).toDateString()
                    return today === subDate
                  }).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <MapPin className="h-8 w-8 text-purple-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">搜索结果</p>
                <p className="text-2xl font-bold">{filteredSubscriptions.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 订阅列表 */}
      <Card>
        <CardHeader>
          <CardTitle>订阅列表</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p>加载中...</p>
            </div>
          ) : filteredSubscriptions.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Mail className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>暂无订阅记录</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4">ID</th>
                    <th className="text-left py-3 px-4">邮箱地址</th>
                    <th className="text-left py-3 px-4">IP地址</th>
                    <th className="text-left py-3 px-4">订阅时间</th>
                    <th className="text-left py-3 px-4">状态</th>
                    <th className="text-left py-3 px-4">操作</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredSubscriptions.map((subscription) => (
                    <tr key={subscription.id} className="border-b hover:bg-gray-50">
                      <td className="py-3 px-4">{subscription.id}</td>
                      <td className="py-3 px-4">
                        <div className="flex items-center">
                          <Mail className="h-4 w-4 mr-2 text-gray-400" />
                          {subscription.subscribe_mail}
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <code className="bg-gray-100 px-2 py-1 rounded text-sm">
                          {subscription.ip_address}
                        </code>
                      </td>
                      <td className="py-3 px-4">
                        {formatDate(subscription.created_at)}
                      </td>
                      <td className="py-3 px-4">
                        <Badge variant={subscription.is_active ? "default" : "secondary"}>
                          {subscription.is_active ? "激活" : "停用"}
                        </Badge>
                      </td>
                      <td className="py-3 px-4">
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => handleDelete(subscription.id, subscription.subscribe_mail)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
} 
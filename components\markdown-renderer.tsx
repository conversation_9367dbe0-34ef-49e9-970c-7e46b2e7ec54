"use client"

import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeRaw from 'rehype-raw'
import rehypeSanitize from 'rehype-sanitize'
import { ContentImage } from '@/app/actions/blog-actions-db-final'

interface MarkdownRendererProps {
  content: string
  contentImages?: ContentImage[] // Keep for backward compatibility but not used for automatic insertion
  className?: string
}

export default function MarkdownRenderer({ content, className = "" }: MarkdownRendererProps) {
  // Use content directly without automatic image insertion
  const processedContent = content

  // Custom components for rendering
  const components = {
    // Custom image renderer to handle alignment
    img: ({ src, alt, ...props }: any) => {
      // Check if this is a custom alignment syntax
      const alignMatch = alt?.match(/^(.*?)\{align:(left|right|center)\}$/)
      if (alignMatch) {
        const [, actualAlt, alignment] = alignMatch
        return (
          <img
            src={src}
            alt={actualAlt}
            align={alignment}
            loading="lazy"
            className={`
              ${alignment === 'left' ? 'float-left mr-6 mb-4 mt-2 max-w-[240px] w-[240px] h-[135px] object-cover' : ''}
              ${alignment === 'right' ? 'float-right ml-6 mb-4 mt-2 max-w-[240px] w-[240px] h-[135px] object-cover' : ''}
              ${alignment === 'center' ? 'block mx-auto my-8 max-w-[480px] w-[480px] h-[270px] object-cover' : ''}
              max-md:float-none max-md:block max-md:mx-auto max-md:max-w-[280px] max-md:w-[280px] max-md:h-[158px] max-md:mb-6
            `}
            {...props}
          />
        )
      }

      // Handle standard markdown image syntax with alignment
      const parentText = props.node?.parent?.children?.find((child: any) => 
        child.type === 'text' && child.value?.includes('{align:')
      )?.value

      if (parentText) {
        const alignMatch = parentText.match(/\{align:(left|right|center)\}/)
        if (alignMatch) {
          const alignment = alignMatch[1]
          return (
            <img
              src={src}
              alt={alt}
              align={alignment}
              loading="lazy"
              className={`
                ${alignment === 'left' ? 'float-left mr-6 mb-4 mt-2 max-w-[240px] w-[240px] h-[135px] object-cover' : ''}
                ${alignment === 'right' ? 'float-right ml-6 mb-4 mt-2 max-w-[240px] w-[240px] h-[135px] object-cover' : ''}
                ${alignment === 'center' ? 'block mx-auto my-8 max-w-[480px] w-[480px] h-[270px] object-cover' : ''}
                max-md:float-none max-md:block max-md:mx-auto max-md:max-w-[280px] max-md:w-[280px] max-md:h-[158px] max-md:mb-6
              `}
              {...props}
            />
          )
        }
      }

      // Default image rendering
      return (
        <img
          src={src}
          alt={alt}
          loading="lazy"
          className="block mx-auto my-8 max-w-full h-auto"
          {...props}
        />
      )
    },

    // Custom paragraph renderer
    p: ({ children, ...props }: any) => (
      <p className="mb-6 leading-[1.8] text-justify hyphens-auto text-[14px]" {...props}>
        {children}
      </p>
    ),

    // Custom heading renderers
    h1: ({ children, ...props }: any) => (
      <h1 className="mt-12 mb-6 text-3xl font-bold text-gray-800 clear-both" {...props}>
        {children}
      </h1>
    ),
    h2: ({ children, ...props }: any) => (
      <h2 className="mt-12 mb-6 text-2xl font-semibold text-gray-800 clear-both" {...props}>
        {children}
      </h2>
    ),
    h3: ({ children, ...props }: any) => (
      <h3 className="mt-10 mb-4 text-xl font-semibold text-gray-700 clear-both" {...props}>
        {children}
      </h3>
    ),

    // Custom blockquote renderer
    blockquote: ({ children, ...props }: any) => (
      <blockquote className="border-l-4 border-red-600 pl-6 my-8 italic text-gray-600 bg-gray-50 py-4 clear-both" {...props}>
        {children}
      </blockquote>
    ),

    // Custom list renderers
    ul: ({ children, ...props }: any) => (
      <ul className="my-6 pl-6 list-disc" {...props}>
        {children}
      </ul>
    ),
    ol: ({ children, ...props }: any) => (
      <ol className="my-6 pl-6 list-decimal" {...props}>
        {children}
      </ol>
    ),
    li: ({ children, ...props }: any) => (
      <li className="mb-3 leading-relaxed" {...props}>
        {children}
      </li>
    ),

    // Custom table renderers
    table: ({ children, ...props }: any) => (
      <div className="overflow-x-auto my-8">
        <table className="min-w-full border border-gray-200 shadow-md rounded-lg overflow-hidden" {...props}>
          {children}
        </table>
      </div>
    ),
    thead: ({ children, ...props }: any) => (
      <thead className="bg-gray-800 text-white" {...props}>
        {children}
      </thead>
    ),
    th: ({ children, ...props }: any) => (
      <th className="py-3 px-4 text-left font-semibold" {...props}>
        {children}
      </th>
    ),
    tbody: ({ children, ...props }: any) => (
      <tbody {...props}>
        {children}
      </tbody>
    ),
    tr: ({ children, ...props }: any) => (
      <tr className="even:bg-gray-50 odd:bg-white" {...props}>
        {children}
      </tr>
    ),
    td: ({ children, ...props }: any) => (
      <td className="py-3 px-4 border-b" {...props}>
        {children}
      </td>
    ),

    // Custom code renderers
    code: ({ children, className, ...props }: any) => {
      const isInline = !className
      if (isInline) {
        return (
          <code className="bg-gray-100 px-2 py-1 rounded text-sm font-mono" {...props}>
            {children}
          </code>
        )
      }
      return (
        <code className="block bg-gray-100 p-4 rounded-lg text-sm font-mono overflow-x-auto" {...props}>
          {children}
        </code>
      )
    },
    pre: ({ children, ...props }: any) => (
      <pre className="bg-gray-100 p-4 rounded-lg text-sm font-mono overflow-x-auto my-6" {...props}>
        {children}
      </pre>
    ),
  }

  return (
    <div className={`prose prose-lg max-w-none ${className}`}>
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeRaw, rehypeSanitize]}
        components={components}
      >
        {processedContent}
      </ReactMarkdown>
    </div>
  )
}

"use client"

import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeRaw from 'rehype-raw'
import rehypeSanitize from 'rehype-sanitize'
import { ContentImage } from '@/app/actions/blog-actions-db-final'

interface MarkdownRendererProps {
  content: string
  contentImages?: ContentImage[]
  className?: string
}

export default function MarkdownRenderer({ content, contentImages, className = "" }: MarkdownRendererProps) {
  // Enhanced content processing function with database images and smart insertion
  function enhanceContentWithImages(markdownContent: string, images?: ContentImage[]): string {
    let processedContent = markdownContent

    // Use database images if available, otherwise fallback to sample images
    const imagesToUse = images && images.length > 0
      ? images
      : [
          {
            id: 'sample1',
            url: '/gun-drill-anatomy.png',
            alt: 'Industrial milling tool showcase',
            position: 'left' as const
          },
          {
            id: 'sample2',
            url: '/custom-gun-drill-manufacturing.png',
            alt: 'CNC machining process',
            position: 'center' as const
          },
          {
            id: 'sample3',
            url: '/counterbore-tool.png',
            alt: 'Precision manufacturing equipment',
            position: 'right' as const
          }
        ]

    // Split content into paragraphs
    let paragraphs = processedContent.split('\n\n').filter(p => p.trim().length > 0)

    // If we don't have enough paragraphs, try splitting by sentences
    if (paragraphs.length < 3) {
      // Split by periods followed by space and capital letter (sentence boundaries)
      const sentences = processedContent.split(/\.\s+(?=[A-Z])/).filter(s => s.trim().length > 20)

      // Group sentences into paragraphs (2-3 sentences per paragraph)
      paragraphs = []
      for (let i = 0; i < sentences.length; i += 2) {
        const paragraph = sentences.slice(i, i + 2).join('. ')
        if (paragraph.trim().length > 0) {
          paragraphs.push(paragraph + (paragraph.endsWith('.') ? '' : '.'))
        }
      }
    }

    // Smart image insertion strategy
    if (paragraphs.length > 1 && imagesToUse.length > 0) {
      let imageIndex = 0

      // Insert first image after opening paragraph (if we have at least 2 paragraphs)
      if (paragraphs.length > 1 && imageIndex < imagesToUse.length) {
        const image = imagesToUse[imageIndex]
        const alignment = image.position || 'left'
        paragraphs.splice(1, 0,
          `![${image.alt}](${image.url}){align:${alignment}}`
        )
        imageIndex++
      }

      // Insert images every 2-3 paragraphs for natural flow
      let insertPosition = 3
      while (insertPosition < paragraphs.length && imageIndex < imagesToUse.length) {
        const image = imagesToUse[imageIndex]
        const alignment = image.position || (['left', 'right', 'center'][imageIndex % 3] as 'left' | 'right' | 'center')

        paragraphs.splice(insertPosition, 0,
          `![${image.alt}](${image.url}){align:${alignment}}`
        )
        imageIndex++

        // Move to next insertion point (skip 2-3 paragraphs)
        insertPosition += 3
      }
    }

    return paragraphs.join('\n\n')
  }

  // Process content with images
  const processedContent = enhanceContentWithImages(content, contentImages)

  // Custom components for rendering
  const components = {
    // Custom image renderer to handle alignment
    img: ({ src, alt, ...props }: any) => {
      // Check if this is a custom alignment syntax
      const alignMatch = alt?.match(/^(.*?)\{align:(left|right|center)\}$/)
      if (alignMatch) {
        const [, actualAlt, alignment] = alignMatch
        return (
          <img
            src={src}
            alt={actualAlt}
            align={alignment}
            loading="lazy"
            className={`
              ${alignment === 'left' ? 'float-left mr-6 mb-4 mt-2 max-w-[240px] w-[240px] h-[135px] object-cover' : ''}
              ${alignment === 'right' ? 'float-right ml-6 mb-4 mt-2 max-w-[240px] w-[240px] h-[135px] object-cover' : ''}
              ${alignment === 'center' ? 'block mx-auto my-8 max-w-[480px] w-[480px] h-[270px] object-cover' : ''}
              max-md:float-none max-md:block max-md:mx-auto max-md:max-w-[280px] max-md:w-[280px] max-md:h-[158px] max-md:mb-6
            `}
            {...props}
          />
        )
      }

      // Handle standard markdown image syntax with alignment
      const parentText = props.node?.parent?.children?.find((child: any) => 
        child.type === 'text' && child.value?.includes('{align:')
      )?.value

      if (parentText) {
        const alignMatch = parentText.match(/\{align:(left|right|center)\}/)
        if (alignMatch) {
          const alignment = alignMatch[1]
          return (
            <img
              src={src}
              alt={alt}
              align={alignment}
              loading="lazy"
              className={`
                ${alignment === 'left' ? 'float-left mr-6 mb-4 mt-2 max-w-[240px] w-[240px] h-[135px] object-cover' : ''}
                ${alignment === 'right' ? 'float-right ml-6 mb-4 mt-2 max-w-[240px] w-[240px] h-[135px] object-cover' : ''}
                ${alignment === 'center' ? 'block mx-auto my-8 max-w-[480px] w-[480px] h-[270px] object-cover' : ''}
                max-md:float-none max-md:block max-md:mx-auto max-md:max-w-[280px] max-md:w-[280px] max-md:h-[158px] max-md:mb-6
              `}
              {...props}
            />
          )
        }
      }

      // Default image rendering
      return (
        <img
          src={src}
          alt={alt}
          loading="lazy"
          className="block mx-auto my-8 max-w-full h-auto"
          {...props}
        />
      )
    },

    // Custom paragraph renderer
    p: ({ children, ...props }: any) => (
      <p className="mb-6 leading-[1.8] text-justify hyphens-auto" {...props}>
        {children}
      </p>
    ),

    // Custom heading renderers
    h1: ({ children, ...props }: any) => (
      <h1 className="mt-12 mb-6 text-3xl font-bold text-gray-800 clear-both" {...props}>
        {children}
      </h1>
    ),
    h2: ({ children, ...props }: any) => (
      <h2 className="mt-12 mb-6 text-2xl font-semibold text-gray-800 clear-both" {...props}>
        {children}
      </h2>
    ),
    h3: ({ children, ...props }: any) => (
      <h3 className="mt-10 mb-4 text-xl font-semibold text-gray-700 clear-both" {...props}>
        {children}
      </h3>
    ),

    // Custom blockquote renderer
    blockquote: ({ children, ...props }: any) => (
      <blockquote className="border-l-4 border-red-600 pl-6 my-8 italic text-gray-600 bg-gray-50 py-4 clear-both" {...props}>
        {children}
      </blockquote>
    ),

    // Custom list renderers
    ul: ({ children, ...props }: any) => (
      <ul className="my-6 pl-6 list-disc" {...props}>
        {children}
      </ul>
    ),
    ol: ({ children, ...props }: any) => (
      <ol className="my-6 pl-6 list-decimal" {...props}>
        {children}
      </ol>
    ),
    li: ({ children, ...props }: any) => (
      <li className="mb-3 leading-relaxed" {...props}>
        {children}
      </li>
    ),

    // Custom table renderers
    table: ({ children, ...props }: any) => (
      <div className="overflow-x-auto my-8">
        <table className="min-w-full border border-gray-200 shadow-md rounded-lg overflow-hidden" {...props}>
          {children}
        </table>
      </div>
    ),
    thead: ({ children, ...props }: any) => (
      <thead className="bg-gray-800 text-white" {...props}>
        {children}
      </thead>
    ),
    th: ({ children, ...props }: any) => (
      <th className="py-3 px-4 text-left font-semibold" {...props}>
        {children}
      </th>
    ),
    tbody: ({ children, ...props }: any) => (
      <tbody {...props}>
        {children}
      </tbody>
    ),
    tr: ({ children, ...props }: any) => (
      <tr className="even:bg-gray-50 odd:bg-white" {...props}>
        {children}
      </tr>
    ),
    td: ({ children, ...props }: any) => (
      <td className="py-3 px-4 border-b" {...props}>
        {children}
      </td>
    ),

    // Custom code renderers
    code: ({ children, className, ...props }: any) => {
      const isInline = !className
      if (isInline) {
        return (
          <code className="bg-gray-100 px-2 py-1 rounded text-sm font-mono" {...props}>
            {children}
          </code>
        )
      }
      return (
        <code className="block bg-gray-100 p-4 rounded-lg text-sm font-mono overflow-x-auto" {...props}>
          {children}
        </code>
      )
    },
    pre: ({ children, ...props }: any) => (
      <pre className="bg-gray-100 p-4 rounded-lg text-sm font-mono overflow-x-auto my-6" {...props}>
        {children}
      </pre>
    ),
  }

  return (
    <div className={`prose prose-lg max-w-none ${className}`}>
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeRaw, rehypeSanitize]}
        components={components}
      >
        {processedContent}
      </ReactMarkdown>
    </div>
  )
}

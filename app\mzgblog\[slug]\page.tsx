import Image from "next/image"
import Link from "next/link"
import { notFound } from "next/navigation"
import { formatDate } from "@/lib/utils"
import { getBlogPost } from "@/app/actions/blog-actions-db-final"
import { Button } from "@/components/ui/button"
import { ArrowLeft, Share2, Calendar, User } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import MarkdownRenderer from "@/components/markdown-renderer"
import type { Metadata } from "next"

interface BlogPostPageProps {
  params: Promise<{
    slug: string
  }>
}

export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  const resolvedParams = await params
  const post = await getBlogPost(resolvedParams.slug)

  if (!post) {
    return {
      title: "Post Not Found | MZG Tools",
    }
  }

  return {
    title: `${post.title} | MZG Tools Blog`,
    description: post.excerpt,
  }
}

// Safe image URL function to handle invalid URLs
function getSafeImageUrl(imageUrl?: string): string {
  const defaultImage = "/placeholder.svg?height=800&width=1200&query=industrial tool"

  if (!imageUrl) {
    return defaultImage
  }

  // Check if it's a relative path starting with /
  if (imageUrl.startsWith('/')) {
    return imageUrl
  }

  // For absolute URLs, try to validate them
  try {
    new URL(imageUrl)
    return imageUrl
  } catch {
    return defaultImage
  }
}



export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const resolvedParams = await params
  const post = await getBlogPost(resolvedParams.slug)

  if (!post) {
    notFound()
  }

  return (
    <>
      <Header />
      <div className="bg-white">
        {/* Hero Section - Adjusted height to match /mzgblog */}
        <div className="relative bg-white text-gray-900 border-b border-gray-200">
          <div className="container mx-auto px-4 py-8 md:py-12 lg:py-16">
            <div className="grid lg:grid-cols-2 gap-4 lg:gap-8 items-center">
              <div className="max-w-4xl order-2 lg:order-1">
                <div className="inline-block bg-red-600 text-white px-2 py-0.5 rounded-full text-xs font-medium mb-2">
                  {post.category?.name || 'Uncategorized'}
                </div>
                <h1 className="text-lg md:text-xl lg:text-2xl font-bold mb-2 leading-tight text-gray-900">
                  {post.title}
                </h1>
                <p className="text-xs md:text-sm mb-2 text-gray-600 leading-relaxed max-w-2xl">
                  {post.excerpt || 'Discover expert insights and technical knowledge in our comprehensive blog post.'}
                </p>
                <div className="flex flex-wrap items-center gap-2 text-xs text-gray-500">
                  <div className="flex items-center gap-1">
                    <Calendar size={12} />
                    <span>{formatDate(post.createdAt)}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <User size={12} />
                    <span>{post.author}</span>
                  </div>
                  {post.viewCount > 0 && (
                    <div className="flex items-center gap-1">
                      <span>👁</span>
                      <span>{post.viewCount} views</span>
                    </div>
                  )}
                </div>
              </div>
              <div className="flex justify-center lg:justify-end order-1 lg:order-2">
                <div className="relative w-full max-w-[280px] lg:max-w-[320px] ml-[-45px]">
                  <div className="aspect-[16/9] bg-gray-50">
                    <Image
                      src={getSafeImageUrl(post.imageUrl)}
                      alt={post.featuredImageAlt || post.title}
                      width={320}
                      height={180}
                      className="w-full h-full object-cover"
                      priority
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 md:px-8 py-8">
          <div className="grid lg:grid-cols-4 gap-8">
            {/* Main Article */}
            <div className="lg:col-span-3">
              {/* Meta Information */}
              <div className="flex flex-wrap items-center gap-4 py-6 border-b border-gray-200 text-gray-600">
                <div className="ml-auto">
                  <Button variant="outline" size="sm" className="flex items-center gap-2">
                    <Share2 size={16} />
                    Share
                  </Button>
                </div>
              </div>

              {/* Article Content */}
              <article className="py-8 leading-relaxed">
                <MarkdownRenderer
                  content={post.content}
                  className="article-content"
                />
              </article>

              {/* Tags */}
              {post.tags && post.tags.length > 0 && (
                <div className="flex flex-wrap gap-2 mb-8">
                  {post.tags.map((tag) => (
                    <Link
                      key={tag.id}
                      href={`/mzgblog?tag=${tag.slug}`}
                      className="bg-gray-100 hover:bg-gray-200 text-gray-800 px-3 py-1 rounded-full text-sm transition-colors"
                    >
                      #{tag.name}
                    </Link>
                  ))}
                </div>
              )}

              {/* Navigation */}
              <div className="mt-12 border-t border-gray-200 pt-8">
                <Link href="/mzgblog">
                  <Button variant="outline" className="flex items-center gap-2">
                    <ArrowLeft size={16} />
                    Back to Blog
                  </Button>
                </Link>
              </div>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className="sticky top-8">
                {/* Recent Posts */}
                <div className="bg-gray-50 rounded-lg p-6 mb-6">
                  <h3 className="text-lg font-bold mb-4">Recent Posts</h3>
                  <div className="space-y-3">
                    <Link href="/mzgblog" className="block text-sm text-gray-600 hover:text-red-600 transition-colors">
                      View all blog posts
                    </Link>
                  </div>
                </div>

                {/* Categories */}
                <div className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-lg font-bold mb-4">Categories</h3>
                  <div className="space-y-2">
                    <Link href="/mzgblog" className="block text-sm text-gray-600 hover:text-red-600 transition-colors">
                      All Categories
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Related Articles */}
        <div className="bg-gray-50 py-16">
          <div className="container mx-auto px-4 md:px-8">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Related Articles</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-6">
              <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
                <h3 className="text-lg font-bold mb-2">Explore More Content</h3>
                <p className="text-gray-600 text-sm mb-4">
                  Discover more insights and technical articles in our blog.
                </p>
                <Link href="/mzgblog">
                  <Button variant="outline" className="text-sm">
                    View All Posts
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  )
}

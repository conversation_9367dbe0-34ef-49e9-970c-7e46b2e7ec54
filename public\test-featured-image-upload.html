<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Featured Image Upload</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .upload-area.dragover {
            border-color: #007bff;
            background-color: #e3f2fd;
        }
        .preview {
            margin: 20px 0;
        }
        .preview img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
        }
        .result {
            margin: 20px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <h1>Test Featured Image Upload</h1>
    <p>This page tests the featured image upload functionality for the blog system.</p>
    
    <div class="upload-area" id="uploadArea">
        <p>📸 Click here or drag and drop an image to upload</p>
        <p><small>Supports JPG, PNG, GIF (max 10MB)</small></p>
        <input type="file" id="fileInput" accept="image/*" style="display: none;">
        <button type="button" onclick="document.getElementById('fileInput').click()">Choose File</button>
    </div>
    
    <div class="preview" id="preview" style="display: none;">
        <h3>Preview:</h3>
        <img id="previewImg" src="" alt="Preview">
    </div>
    
    <div id="result"></div>
    
    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const preview = document.getElementById('preview');
        const previewImg = document.getElementById('previewImg');
        const result = document.getElementById('result');
        
        // Drag and drop handlers
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });
        
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFile(e.target.files[0]);
            }
        });
        
        function handleFile(file) {
            // Validate file type
            if (!file.type.startsWith('image/')) {
                showResult('Please select an image file.', 'error');
                return;
            }
            
            // Validate file size (10MB)
            if (file.size > 10 * 1024 * 1024) {
                showResult('File size must be less than 10MB.', 'error');
                return;
            }
            
            // Show preview
            const reader = new FileReader();
            reader.onload = (e) => {
                previewImg.src = e.target.result;
                preview.style.display = 'block';
            };
            reader.readAsDataURL(file);
            
            // Upload file
            uploadFile(file);
        }
        
        async function uploadFile(file) {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('type', 'blog-content');
            
            try {
                showResult('Uploading...', 'info');
                
                const response = await fetch('/api/upload', {
                    method: 'POST',
                    body: formData
                });
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.error || `Upload failed (${response.status})`);
                }
                
                const data = await response.json();
                showResult(`✅ Upload successful! URL: ${data.url}`, 'success');
                
            } catch (error) {
                showResult(`❌ Upload failed: ${error.message}`, 'error');
            }
        }
        
        function showResult(message, type) {
            result.innerHTML = `<div class="result ${type}">${message}</div>`;
        }
    </script>
</body>
</html>

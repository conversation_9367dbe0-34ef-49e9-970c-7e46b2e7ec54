import { NextRequest, NextResponse } from "next/server"
import { getCurrentUser } from "@/lib/auth-mzg"

// 强制动态渲染
export const dynamic = 'force-dynamic'
import { sendEmail, EmailSettings } from "@/lib/email-service"

// POST - 发送测试邮件
export async function POST(request: NextRequest) {
  try {
    // 验证用户权限
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json({ error: "未授权访问" }, { status: 401 })
    }

    const settings: EmailSettings = await request.json()

    // 验证必需字段
    if (!settings.apiToken) {
      return NextResponse.json(
        { error: "API Token不能为空" },
        { status: 400 }
      )
    }

    if (!settings.fromEmail) {
      return NextResponse.json(
        { error: "发件人邮箱不能为空" },
        { status: 400 }
      )
    }

    if (!settings.testEmail) {
      return NextResponse.json(
        { error: "测试邮箱地址不能为空" },
        { status: 400 }
      )
    }

    // 构建邮件内容
    const htmlBody = `
      <html>
      <head>
        <style>
          body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #dc2626, #ef4444); color: white; padding: 30px; border-radius: 8px 8px 0 0; text-align: center; }
          .content { background: #f9fafb; padding: 30px; border-radius: 0 0 8px 8px; }
          .status { background: #dcfce7; border: 1px solid #bbf7d0; color: #166534; padding: 15px; border-radius: 6px; margin: 20px 0; }
          .info { background: #eff6ff; border: 1px solid #bfdbfe; color: #1e40af; padding: 15px; border-radius: 6px; margin: 20px 0; }
          .footer { text-align: center; margin-top: 30px; color: #6b7280; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🎉 邮件系统测试成功</h1>
            <p>MZG Tools 管理系统</p>
          </div>
          <div class="content">
            <div class="status">
              <strong>✅ 测试状态：</strong> 邮件系统配置正常，连接成功！
            </div>
            
            <h3>📧 邮件配置信息</h3>
            <div class="info">
              <p><strong>发件人：</strong> ${settings.fromEmail}</p>
              <p><strong>收件人：</strong> ${settings.testEmail}</p>
              <p><strong>消息流：</strong> ${settings.stream}</p>
              <p><strong>测试时间：</strong> ${new Date().toLocaleString('zh-CN')}</p>
            </div>

            <h3>🔧 接收者配置</h3>
            <div class="info">
              <p><strong>主收件箱：</strong> ${settings.recipients.to || '未设置'}</p>
              <p><strong>抄送 (CC)：</strong> ${settings.recipients.cc || '未设置'}</p>
              <p><strong>密送 (BCC)：</strong> ${settings.recipients.bcc || '未设置'}</p>
            </div>

            <p>如果您能收到这封邮件，说明您的 Postmark 邮件服务配置已经正确设置。您现在可以在 MZG Tools 管理系统中正常使用邮件功能了。</p>
            
            <p><strong>注意事项：</strong></p>
            <ul>
              <li>请确保发件人邮箱已在 Postmark 中完成验证</li>
              <li>检查垃圾邮件文件夹，测试邮件可能被误判为垃圾邮件</li>
              <li>如有问题，请联系系统管理员</li>
            </ul>
          </div>
          <div class="footer">
            <p>此邮件由 MZG Tools 管理系统自动发送</p>
            <p>测试邮件 ID: ${Math.random().toString(36).substr(2, 9).toUpperCase()}</p>
          </div>
        </div>
      </body>
      </html>
    `

    const textBody = `
MZG Tools 邮件系统测试

测试状态：邮件系统配置正常，连接成功！

邮件配置信息：
- 发件人：${settings.fromEmail}
- 收件人：${settings.testEmail}  
- 消息流：${settings.stream}
- 测试时间：${new Date().toLocaleString('zh-CN')}

接收者配置：
- 主收件箱：${settings.recipients.to || '未设置'}
- 抄送 (CC)：${settings.recipients.cc || '未设置'}
- 密送 (BCC)：${settings.recipients.bcc || '未设置'}

如果您能收到这封邮件，说明您的 Postmark 邮件服务配置已经正确设置。

注意事项：
- 请确保发件人邮箱已在 Postmark 中完成验证
- 检查垃圾邮件文件夹，测试邮件可能被误判为垃圾邮件  
- 如有问题，请联系系统管理员

此邮件由 MZG Tools 管理系统自动发送
测试邮件 ID: ${Math.random().toString(36).substr(2, 9).toUpperCase()}
    `

    // 使用邮件服务库发送邮件
    const result = await sendEmail({
      to: settings.testEmail,
      cc: settings.recipients.cc || undefined,
      bcc: settings.recipients.bcc || undefined,
      subject: "MZG Tools 邮件系统测试",
      htmlBody,
      textBody,
      from: settings.fromEmail,
      stream: settings.stream
    })

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: "测试邮件发送成功",
        messageId: result.messageId
      })
    } else {
      return NextResponse.json(
        { 
          error: result.error
        },
        { status: 400 }
      )
    }
  } catch (error) {
    console.error("发送测试邮件失败:", error)
    
    return NextResponse.json(
      { error: "发送测试邮件失败，请稍后重试" },
      { status: 500 }
    )
  }
} 
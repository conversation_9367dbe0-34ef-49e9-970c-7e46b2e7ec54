import { NextRequest, NextResponse } from "next/server"
import { getCurrentUser } from "@/lib/auth-mzg"

// 强制动态渲染
export const dynamic = 'force-dynamic'
import { testPostmarkConnection } from "@/lib/email-service"

// POST - 测试Postmark连接
export async function POST(request: NextRequest) {
  try {
    // 验证用户权限
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json({ error: "未授权访问" }, { status: 401 })
    }

    const { apiToken } = await request.json()

    if (!apiToken) {
      return NextResponse.json(
        { error: "API Token不能为空" },
        { status: 400 }
      )
    }

    // 使用邮件服务库测试连接
    const result = await testPostmarkConnection(apiToken)

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: "Postmark连接测试成功",
        serverInfo: result.serverInfo
      })
    } else {
      return NextResponse.json(
        { 
          error: result.error
        },
        { status: 400 }
      )
    }
  } catch (error) {
    console.error("Postmark连接测试失败:", error)
    
    // 处理网络错误
    if (error instanceof TypeError && error.message.includes("fetch")) {
      return NextResponse.json(
        { error: "网络连接失败，请检查网络设置" },
        { status: 500 }
      )
    }

    return NextResponse.json(
      { error: "连接测试失败，请稍后重试" },
      { status: 500 }
    )
  }
} 
import Link from "next/link"
import { getCurrentUser } from "@/lib/auth-mzg"

// 强制动态渲染
export const dynamic = 'force-dynamic'
import { redirect } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Mail, Settings, Database, Bell } from "lucide-react"

export default async function SettingsPage() {
  // 检查用户是否已登录
  const user = await getCurrentUser()
  if (!user) {
    redirect("/admin-mzg/login")
  }

  const settingsItems = [
    {
      title: "邮箱设置",
      description: "配置邮件服务器，设置收发件人信息",
      href: "/admin-mzg/settings/email",
      icon: Mail,
      color: "bg-blue-50 text-blue-600 border-blue-200"
    },
    {
      title: "系统配置",
      description: "基本系统设置和参数配置",
      href: "/admin-mzg/settings/system",
      icon: Settings,
      color: "bg-gray-50 text-gray-600 border-gray-200"
    },
    {
      title: "数据库设置",
      description: "数据库连接和备份配置",
      href: "/admin-mzg/settings/database",
      icon: Database,
      color: "bg-green-50 text-green-600 border-green-200"
    },
    {
      title: "通知设置",
      description: "系统通知和警报配置",
      href: "/admin-mzg/settings/notifications",
      icon: Bell,
      color: "bg-yellow-50 text-yellow-600 border-yellow-200"
    }
  ]

  return (
    <div className="p-6">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">系统设置</h1>
        <p className="text-gray-600 mt-2">管理系统的各项配置选项</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {settingsItems.map((item) => {
          const Icon = item.icon
          return (
            <Card key={item.href} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg border ${item.color}`}>
                    <Icon className="h-5 w-5" />
                  </div>
                  <div>
                    <CardTitle className="text-lg">{item.title}</CardTitle>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-sm text-gray-600 mb-4">
                  {item.description}
                </CardDescription>
                <Link href={item.href}>
                  <Button variant="outline" size="sm" className="w-full">
                    配置设置
                  </Button>
                </Link>
              </CardContent>
            </Card>
          )
        })}
      </div>
    </div>
  )
} 
import { NextRequest, NextResponse } from "next/server"
import { getCurrentUser } from "@/lib/auth-mzg"

// 强制动态渲染
export const dynamic = 'force-dynamic'
import { getEmailSettings, saveEmailSettings, EmailSettings } from "@/lib/email-service"

// GET - 获取邮箱设置
export async function GET() {
  try {
    // 验证用户权限
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json({ error: "未授权访问" }, { status: 401 })
    }

    const settings = await getEmailSettings()
    
    // 返回设置时隐藏API token的完整内容
    const publicSettings = {
      ...settings,
      apiToken: settings.apiToken ? "••••••••••••••••••••••••••••••••" : ""
    }
    
    return NextResponse.json(publicSettings)
  } catch (error) {
    console.error("获取邮箱设置失败:", error)
    return NextResponse.json(
      { error: "获取邮箱设置失败" },
      { status: 500 }
    )
  }
}

// POST - 保存邮箱设置
export async function POST(request: NextRequest) {
  try {
    // 验证用户权限
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json({ error: "未授权访问" }, { status: 401 })
    }

    const body = await request.json()
    
    // 验证必需字段
    if (!body.fromEmail) {
      return NextResponse.json(
        { error: "发件人邮箱不能为空" },
        { status: 400 }
      )
    }

    // 如果API token是隐藏的，保持原有值
    let settings = await getEmailSettings()
    if (body.apiToken === "••••••••••••••••••••••••••••••••") {
      body.apiToken = settings.apiToken
    }

    // 保存设置
    const newSettings: EmailSettings = {
      apiToken: body.apiToken || "",
      fromEmail: body.fromEmail,
      stream: body.stream || "outbound",
      recipients: {
        to: body.recipients?.to || "",
        cc: body.recipients?.cc || "",
        bcc: body.recipients?.bcc || ""
      },
      testEmail: body.testEmail || ""
    }

    await saveEmailSettings(newSettings)

    return NextResponse.json({ 
      success: true,
      message: "邮箱设置保存成功" 
    })
  } catch (error) {
    console.error("保存邮箱设置失败:", error)
    return NextResponse.json(
      { error: "保存邮箱设置失败" },
      { status: 500 }
    )
  }
} 
"use client"

import { useState, useCallback, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Upload, X, Image as ImageIcon, Trash2 } from "lucide-react"

interface FeaturedImageUploadProps {
  imageUrl: string
  altText: string
  onImageChange: (url: string) => void
  onAltTextChange: (alt: string) => void
  onImageDelete: () => void
}

export default function FeaturedImageUpload({
  imageUrl,
  altText,
  onImageChange,
  onAltTextChange,
  onImageDelete,
}: FeaturedImageUploadProps) {
  const [uploading, setUploading] = useState(false)
  const [dragActive, setDragActive] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Compress image before upload
  const compressImage = useCallback((file: File): Promise<Blob | null> => {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()

      img.onload = () => {
        // Calculate new dimensions (max 1200px width, maintain aspect ratio)
        const maxWidth = 1200
        const maxHeight = 800
        let { width, height } = img

        if (width > maxWidth) {
          height = (height * maxWidth) / width
          width = maxWidth
        }
        if (height > maxHeight) {
          width = (width * maxHeight) / height
          height = maxHeight
        }

        canvas.width = width
        canvas.height = height

        // Draw and compress
        ctx?.drawImage(img, 0, 0, width, height)
        canvas.toBlob(resolve, 'image/jpeg', 0.8)
      }

      img.src = URL.createObjectURL(file)
    })
  }, [])

  // Upload image to server
  const uploadImage = useCallback(async (file: File): Promise<string> => {
    try {
      console.log('🚀 开始上传特色图片:', file.name, 'Size:', file.size)

      // Compress image
      const compressedBlob = await compressImage(file)
      if (!compressedBlob) {
        throw new Error('图片压缩失败')
      }

      const compressedFile = new File([compressedBlob], file.name, { type: 'image/jpeg' })
      console.log('📦 压缩后大小:', compressedFile.size)

      const formData = new FormData()
      formData.append('file', compressedFile)
      formData.append('type', 'blog-content')

      console.log('📤 发送上传请求到 /api/upload...')
      
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 30000) // 30秒超时

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
        signal: controller.signal,
      })

      clearTimeout(timeoutId)
      console.log('📥 上传响应状态:', response.status)

      if (!response.ok) {
        const responseText = await response.text()
        console.error('❌ 响应内容:', responseText)
        
        let errorData
        try {
          errorData = JSON.parse(responseText)
        } catch {
          errorData = { error: responseText || 'Unknown error' }
        }
        
        throw new Error(errorData.error || `上传失败 (${response.status})`)
      }

      const data = await response.json()
      console.log('✅ 上传成功:', data)
      return data.url
    } catch (error) {
      console.error('❌ 上传特色图片错误:', error)
      
      if (error.name === 'AbortError') {
        throw new Error('上传超时，请检查网络连接')
      }
      
      if (error.message.includes('Failed to fetch')) {
        throw new Error('网络连接失败，请检查服务器状态')
      }
      
      throw error
    }
  }, [compressImage])

  const handleFileSelect = useCallback(async (files: FileList | null) => {
    if (!files || files.length === 0) return

    const file = files[0]
    
    // Validate file type
    if (!file.type.startsWith('image/')) {
      alert('请选择图片文件')
      return
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      alert('图片文件大小不能超过 10MB')
      return
    }

    setUploading(true)
    try {
      const url = await uploadImage(file)
      onImageChange(url)
      
      // Auto-generate alt text from filename if not provided
      if (!altText) {
        const filename = file.name.replace(/\.[^/.]+$/, "")
        const autoAlt = filename.replace(/[-_]/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
        onAltTextChange(autoAlt)
      }
    } catch (error) {
      console.error('Upload failed:', error)
      alert(`上传失败: ${error.message}`)
    } finally {
      setUploading(false)
    }
  }, [uploadImage, onImageChange, onAltTextChange, altText])

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files)
    }
  }, [handleFileSelect])

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    handleFileSelect(e.target.files)
  }, [handleFileSelect])

  const handleButtonClick = useCallback(() => {
    fileInputRef.current?.click()
  }, [])

  const handleDelete = useCallback(() => {
    if (confirm('确定要删除这张特色图片吗？')) {
      onImageDelete()
    }
  }, [onImageDelete])

  return (
    <div className="space-y-4">
      {/* Current Image Display */}
      {imageUrl && (
        <div className="relative">
          <img
            src={imageUrl}
            alt={altText || "Featured image preview"}
            className="w-full h-48 object-cover rounded-lg border"
            onError={(e) => {
              e.currentTarget.style.display = 'none'
            }}
          />
          <Button
            type="button"
            variant="destructive"
            size="sm"
            className="absolute top-2 right-2"
            onClick={handleDelete}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      )}

      {/* Upload Area */}
      <div
        className={`relative border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
          dragActive
            ? 'border-blue-400 bg-blue-50'
            : uploading
            ? 'border-gray-300 bg-gray-50'
            : 'border-gray-300 hover:border-gray-400'
        }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleInputChange}
          className="hidden"
          disabled={uploading}
        />

        {uploading ? (
          <div className="space-y-2">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-sm text-gray-600">上传中...</p>
          </div>
        ) : (
          <div className="space-y-2">
            <ImageIcon className="h-8 w-8 text-gray-400 mx-auto" />
            <div>
              <Button
                type="button"
                variant="outline"
                onClick={handleButtonClick}
                className="mb-2"
              >
                <Upload className="h-4 w-4 mr-2" />
                选择图片
              </Button>
              <p className="text-sm text-gray-600">
                或拖拽图片到此处
              </p>
              <p className="text-xs text-gray-500 mt-1">
                支持 JPG, PNG, GIF 格式，最大 10MB
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Alt Text Input */}
      <div>
        <Label htmlFor="featuredImageAlt">图片描述 (Alt Text)</Label>
        <Input
          id="featuredImageAlt"
          value={altText}
          onChange={(e) => onAltTextChange(e.target.value)}
          placeholder="描述这张图片的内容"
          className="mt-1"
        />
        <p className="text-sm text-gray-600 mt-1">
          用于SEO和无障碍访问，建议填写
        </p>
      </div>
    </div>
  )
}

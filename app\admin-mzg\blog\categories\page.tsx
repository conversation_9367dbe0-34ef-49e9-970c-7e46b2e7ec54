import { getCurrentUser } from "@/lib/auth-mzg"
import { redirect } from "next/navigation"
import CategoriesManagePageClient from "./categories-client"

// 强制动态渲染
export const dynamic = 'force-dynamic'

export default async function CategoriesManagePage() {
  // 服务器端身份验证检查
  const user = await getCurrentUser()
  if (!user) {
    redirect("/admin-mzg/login")
  }

  return <CategoriesManagePageClient />
}
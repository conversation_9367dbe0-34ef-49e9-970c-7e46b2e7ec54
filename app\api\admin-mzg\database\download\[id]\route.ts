import { NextRequest, NextResponse } from "next/server"
import { sql } from "@/lib/database"

// 强制动态渲染
export const dynamic = 'force-dynamic'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const backupId = params.id
    console.log('下载备份文件:', backupId)
    
    // 获取备份记录
    const backup = await sql`
      SELECT * FROM backup_history WHERE id = ${backupId}
    `
    
    if (backup.length === 0) {
      return NextResponse.json(
        { success: false, message: '备份文件不存在' },
        { status: 404 }
      )
    }
    
    const backupRecord = backup[0]
    
    // 在 Vercel 环境中，我们不能依赖文件系统存储
    // 改为从数据库中直接返回备份数据
    try {
      // 如果备份记录中有内容数据，直接返回
      if (backupRecord.content_data) {
        const mimeType = backupRecord.format === 'json'
          ? 'application/json'
          : 'text/csv'

        return new Response(backupRecord.content_data, {
          headers: {
            'Content-Type': mimeType,
            'Content-Disposition': `attachment; filename="${backupRecord.original_filename}"`,
            'Cache-Control': 'no-cache'
          }
        })
      }

      // 如果没有内容数据，返回错误
      return NextResponse.json(
        { success: false, message: '备份文件内容不可用，请重新创建备份' },
        { status: 404 }
      )

    } catch (fileError) {
      console.error('生成备份文件失败:', fileError)
      return NextResponse.json(
        { success: false, message: '备份文件生成失败' },
        { status: 500 }
      )
    }
    
  } catch (err) {
    console.error('下载备份文件失败:', err)
    const errorMessage = err instanceof Error ? err.message : '未知错误'
    return NextResponse.json(
      { success: false, message: `下载失败: ${errorMessage}` },
      { status: 500 }
    )
  }
} 